{"name": "deeplinked", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next build", "deploy": "npm run export && firebase deploy"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@tanstack/react-query": "^5.80.6", "@tanstack/react-virtual": "^3.13.9", "date-fns": "^4.1.0", "firebase": "^11.8.1", "firebase-admin": "^13.4.0", "firebase-functions": "^6.3.2", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "slugify": "^1.6.6", "zod": "^3.25.51"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}